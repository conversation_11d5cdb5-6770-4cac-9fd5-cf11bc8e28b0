#!/bin/bash

# 修复后的 VGOP1-R2.10-24201day.sh 脚本
# 主要修复：
# 1. 参数验证和默认值处理
# 2. 条件判断语法错误修复
# 3. 文件存在性检查
# 4. 数据库连接错误处理
# 5. 变量安全性改进

# 参数验证和默认值设置
MmeActionInsId="${1:-}"
MmeImagePath="${2:-}"
MmeDateId="${3:-}"
SrcDBName="${4:-}"
ColSep="${5:-}"

# 参数验证
if [ -z "$MmeImagePath" ]; then
    echo "错误: MmeImagePath 参数不能为空"
    echo "用法: $0 MmeActionInsId MmeImagePath MmeDateId SrcDBName ColSep"
    exit 1
fi

if [ -z "$MmeDateId" ]; then
    echo "错误: MmeDateId 参数不能为空"
    echo "用法: $0 MmeActionInsId MmeImagePath MmeDateId SrcDBName ColSep"
    exit 1
fi

if [ -z "$SrcDBName" ]; then
    echo "错误: SrcDBName 参数不能为空，请提供数据库名称"
    echo "用法: $0 MmeActionInsId MmeImagePath MmeDateId SrcDBName ColSep"
    exit 1
fi

# 验证目录是否存在
if [ ! -d "$MmeImagePath" ]; then
    echo "错误: 目录 $MmeImagePath 不存在"
    exit 1
fi

ImagePath=$(cd "$MmeImagePath" && pwd)

LogPath=${ImagePath}/log
if [ ! -d "${LogPath}" ]; then
    mkdir -p "${LogPath}"
fi
LogName=${LogPath}/../../user

MmeDate="${MmeDateId:0:8}"

BeforeDay=$(date +"%Y%m%d" -d"${MmeDate} -1day")

starttime=${BeforeDay}"000000"
endtime=${MmeDate}"000000"

if [ -z "$ColSep" ]; then
    ColSep="|"
fi

TmpFileName="a_10000_${BeforeDay}_VGOP1-R2.10-24201.unl"
nowdate=$(date +"%Y%m%d%H%M%S")

# 测试数据库连接
echo "测试数据库连接..." >>${LogName}
if ! echo "select count(*) from systables where tabname='bms_vgop_revtimes'" | dbaccess ${SrcDBName} >/dev/null 2>&1; then
    echo "错误: 无法连接到数据库 ${SrcDBName}" >>${LogName}
    echo "错误: 无法连接到数据库 ${SrcDBName}"
    exit 1
fi

# 数据库查询，添加错误处理
revtimes=$(echo "select * from bms_vgop_revtimes where datatime='${BeforeDay}' and tmpfilename='${TmpFileName}'" | dbaccess ${SrcDBName} 2>>${LogName} | grep 'times' | awk -F" " '{print$2}')

# 安全的条件判断，处理空值情况
if [ -n "${revtimes}" ] && [ "${revtimes}" != "" ]; then
    revtimes=$(expr ${revtimes} + 1)
    if [ $(expr length ${revtimes}) -eq 1 ]; then
        revtimes="0"${revtimes}
    fi
    echo "update bms_vgop_revtimes set times=${revtimes} where datatime='${BeforeDay}' and tmpfilename='${TmpFileName}'" | dbaccess ${SrcDBName} 1>>${LogName} 2>&1
else
    revtimes="00"
    echo "insert into bms_vgop_revtimes(datatime,times,tmpfilename,optime) values('${BeforeDay}','${revtimes}','${TmpFileName}','${nowdate}')" | dbaccess ${SrcDBName} 1>>${LogName} 2>&1
fi

daydatapath=${ImagePath}"/VGOPdata/datafile/"${BeforeDay}"/day/"
UnloadFileName=${daydatapath}${TmpFileName}

if [ ! -d "${daydatapath}" ]; then
    mkdir -p "${daydatapath}" 1>>${LogName} 2>&1
fi

echo "[$MmeActionInsId] unloadFileName="${UnloadFileName} >>${LogName}

sql="select mum.phonenumber as phonenumber, mum.phonestate as phonestate ,mum.phoneimsi as phoneimsi,mum.phoneimei as phoneimei,mum.locationid as locationid,substr(bp.bossid, 1, 3) as provinceid,
mum.openingtime as openingtime,mum.Optime as Optime,mum.sex as sex from mcn_user_major mum left join bossprovince bp on mum.provinceid = bp.provinceid
where mum.openingtime>='${starttime}' and mum.openingtime<'${endtime}' and mum.phonestate in ('0','1')"

UnloadCmd="set lock mode to wait 10;unload to ${UnloadFileName} delimiter '$ColSep' ${sql};"

echo "[$MmeActionInsId]"$(date +"%Y%m%d %H%M%S:")"Unload Start: ${UnloadCmd}" >>${LogName}

echo "${UnloadCmd}" | dbaccess $SrcDBName 1>>${LogName} 2>&1

echo "[$MmeActionInsId]"$(date +"%Y%m%d %H%M%S:")"Unload END." >>${LogName}

# 检查文件是否存在，然后安全地获取行数
if [ -f "${UnloadFileName}" ]; then
    linenum=$(wc -l "${UnloadFileName}" | awk '{print $1}')
    # 确保 linenum 是数字，如果为空或非数字则设为0
    if ! echo "${linenum}" | grep -q '^[0-9]\+$'; then
        linenum=0
    fi
else
    echo "错误: 文件 ${UnloadFileName} 不存在" >>${LogName}
    echo "错误: 数据导出失败，文件 ${UnloadFileName} 不存在"
    linenum=0
fi

# 安全的条件判断
if [ "${linenum}" -eq 0 ]; then
    echo "警告: 没有数据导出或文件为空，设置最小行数为1" >>${LogName}
    linenum=1
fi

filenum=001
n1=1
JYFileName=${daydatapath}"a_10000_${BeforeDay}_VGOP1-R2.10-24201_${revtimes}.verf"

echo "开始处理数据文件，总行数: ${linenum}" >>${LogName}

while [ "${n1}" -le "${linenum}" ]; do 
    n2=$(expr ${n1} + 2000000) 
    FileName=${daydatapath}"a_10000_${BeforeDay}_VGOP1-R2.10-24201_${revtimes}_${filenum}.dat"
    
    if [ -f "${UnloadFileName}" ]; then
        sed -n "${n1},${n2}p" "${UnloadFileName}" | nl -s '|' | sed 's/|$//' | tr -d '\' | tr -d ' ' | tr '|' '\200' | awk '{print($0"\r")}' >"${FileName}"
        echo "$(basename ${FileName}) $(wc -c ${FileName}) $(wc -l ${FileName}) ${BeforeDay} ${nowdate}" | awk '{print($1"|"$2"|"$4"|"$6"|"$7)}' | tr '|' '\200' | awk '{print($0"\r")}' >>"${JYFileName}"
    fi
    
    n1=$(expr ${n2} + 1)
    filenum=$(expr ${filenum} + 1)
    filenumlength=$(echo ${filenum} | awk '{print length($0)}')
    if [ "${filenumlength}" -eq 1 ]; then
        filenum="00"${filenum}
    elif [ "${filenumlength}" -eq 2 ]; then
        filenum="0"${filenum}
    fi
done

echo "[$MmeActionInsId]"$(date +"%Y%m%d %H%M%S:")"脚本执行完成" >>${LogName}
echo "脚本执行完成，请检查日志文件: ${LogName}"
