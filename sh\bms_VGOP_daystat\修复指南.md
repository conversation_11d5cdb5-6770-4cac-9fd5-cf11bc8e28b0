# VGOP1-R2.10-24201day.sh 脚本修复指南

## 问题分析

### 1. 原始错误分析

#### 错误1: 数据库连接错误
```
349: Database not selected yet. Error in line 1 Near character position 33
```
**原因**: 脚本第4个参数 `SrcDBName` 为空字符串，导致 `dbaccess` 命令无法连接数据库。

#### 错误2: 文件不存在错误
```
wc: /home/<USER>/tyg/VGOPdata/datafile/20240103/day/a_10000_20240103_VGOP1-R2.10-24201.unl: No such file or directory
```
**原因**: 由于数据库连接失败，数据导出失败，导致 `.unl` 文件不存在。

#### 错误3: 脚本语法错误
```
./VGOP1-R2.10-24201day.sh: line 68: [: -eq: unary operator expected
./VGOP1-R2.10-24201day.sh: line 74: [: 1: unary operator expected
```
**原因**: 变量为空时，条件判断语句 `[ ${variable} -eq 0 ]` 变成 `[ -eq 0 ]`，导致语法错误。

### 2. 修复方案

#### 修复1: 参数验证
```bash
# 原始代码
SrcDBName=$4

# 修复后代码
SrcDBName="${4:-}"
if [ -z "$SrcDBName" ]; then
    echo "错误: SrcDBName 参数不能为空，请提供数据库名称"
    exit 1
fi
```

#### 修复2: 安全的条件判断
```bash
# 原始代码（第68行）
if [ ${linenum} -eq 0 ];then

# 修复后代码
if [ "${linenum}" -eq 0 ]; then
```

#### 修复3: 文件存在性检查
```bash
# 原始代码
linenum=$(wc -l ${UnloadFileName} | awk '{print $1}')

# 修复后代码
if [ -f "${UnloadFileName}" ]; then
    linenum=$(wc -l "${UnloadFileName}" | awk '{print $1}')
    if ! echo "${linenum}" | grep -q '^[0-9]\+$'; then
        linenum=0
    fi
else
    echo "错误: 文件 ${UnloadFileName} 不存在" >>${LogName}
    linenum=0
fi
```

#### 修复4: 数据库连接测试
```bash
# 添加数据库连接测试
if ! echo "select count(*) from systables where tabname='bms_vgop_revtimes'" | dbaccess ${SrcDBName} >/dev/null 2>&1; then
    echo "错误: 无法连接到数据库 ${SrcDBName}"
    exit 1
fi
```

## 使用说明

### 1. 正确的命令格式
```bash
./VGOP1-R2.10-24201day_fixed.sh "action_id" "/home/<USER>/tyg" "20240104030000" "your_database_name" "|"
```

### 2. 参数说明
- `$1 (MmeActionInsId)`: 操作ID，可以为空
- `$2 (MmeImagePath)`: 数据路径，必须存在的目录路径
- `$3 (MmeDateId)`: 日期ID，格式：YYYYMMDDHHMMSS
- `$4 (SrcDBName)`: 数据库名称，**不能为空**
- `$5 (ColSep)`: 列分隔符，默认为 "|"

### 3. 执行前检查清单

#### 检查1: 数据库连接
```bash
# 测试数据库连接
echo "select count(*) from systables" | dbaccess your_database_name
```

#### 检查2: 目录权限
```bash
# 检查目录是否存在且有写权限
ls -ld /home/<USER>/tyg
mkdir -p /home/<USER>/tyg/VGOPdata/datafile/20240103/day/
```

#### 检查3: 脚本权限
```bash
# 给脚本执行权限
chmod +x VGOP1-R2.10-24201day_fixed.sh
```

### 4. 调试步骤

#### 步骤1: 使用修复后的脚本
```bash
cp VGOP1-R2.10-24201day_fixed.sh VGOP1-R2.10-24201day.sh
chmod +x VGOP1-R2.10-24201day.sh
```

#### 步骤2: 提供正确的数据库名称
```bash
# 替换 your_actual_database_name 为实际的数据库名
./VGOP1-R2.10-24201day.sh "" "/home/<USER>/tyg" "20240104030000" "your_actual_database_name" "|"
```

#### 步骤3: 检查日志
```bash
# 查看日志文件
tail -f /home/<USER>/tyg/log/../../user
```

### 5. 常见问题解决

#### 问题1: 仍然出现数据库连接错误
**解决方案**: 
1. 确认数据库服务正在运行
2. 检查数据库名称是否正确
3. 验证数据库用户权限

#### 问题2: 目录权限问题
**解决方案**:
```bash
sudo chown -R gbasedbt:gbasedbt /home/<USER>/tyg
chmod -R 755 /home/<USER>/tyg
```

#### 问题3: 日期格式问题
**解决方案**: 确保日期参数格式为 `YYYYMMDDHHMMSS`，例如 `20240104030000`

### 6. 验证修复结果

执行修复后的脚本，应该看到：
1. 参数验证通过
2. 数据库连接成功
3. 数据文件正常生成
4. 没有语法错误

如果仍有问题，请检查日志文件获取详细错误信息。
