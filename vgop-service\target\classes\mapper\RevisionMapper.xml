<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vgop.service.dao.RevisionMapper">

    <resultMap id="RevisionResultMap" type="com.vgop.service.entity.RevisionEntity">
        <result column="datatime" property="dataTime"/>
        <result column="times" property="times"/>
        <result column="tmpfilename" property="tmpFileName"/>
        <result column="optime" property="opTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <select id="findByDateAndFileName" resultMap="RevisionResultMap">
        SELECT datatime, times, tmpfilename, optime
        FROM bms_vgop_revtimes
        WHERE datatime = #{dataTime} AND tmpfilename = #{tmpFileName}
    </select>

    <insert id="insert" parameterType="com.vgop.service.entity.RevisionEntity">
        INSERT INTO bms_vgop_revtimes (datatime, times, tmpfilename, optime)
        VALUES (#{dataTime}, #{times}, #{tmpFileName}, #{opTime})
    </insert>

    <update id="updateTimes">
        UPDATE bms_vgop_revtimes
        SET times = #{times}
        WHERE datatime = #{dataTime} AND tmpfilename = #{tmpFileName}
    </update>

</mapper> 