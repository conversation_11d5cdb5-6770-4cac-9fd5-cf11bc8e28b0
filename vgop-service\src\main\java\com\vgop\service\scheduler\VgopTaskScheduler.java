package com.vgop.service.scheduler;

import com.vgop.service.common.MonitorLogger;
import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.entity.RevisionTimes;
import com.vgop.service.entity.TaskExecution;
import com.vgop.service.service.FileTransferService;
import com.vgop.service.service.RevisionTimesService;
import com.vgop.service.service.TaskExecutionService;
import com.vgop.service.util.MdcUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * VGOP任务调度器
 * 负责按照配置的时间调度执行各项任务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class VgopTaskScheduler {
    
    private final VgopAppConfig appConfig;
    private final RevisionTimesService revisionTimesService;
    private final FileTransferService fileTransferService;
    private final TaskExecutionService taskExecutionService;
    private final MonitorLogger monitorLogger;
    
    // 任务锁，防止并发执行
    private final Lock dailyTaskLock = new ReentrantLock();
    private final Lock monthlyTaskLock = new ReentrantLock();
    
    @Autowired
    public VgopTaskScheduler(
            VgopAppConfig appConfig,
            RevisionTimesService revisionTimesService,
            FileTransferService fileTransferService,
            TaskExecutionService taskExecutionService,
            MonitorLogger monitorLogger) {
        this.appConfig = appConfig;
        this.revisionTimesService = revisionTimesService;
        this.fileTransferService = fileTransferService;
        this.taskExecutionService = taskExecutionService;
        this.monitorLogger = monitorLogger;
    }
    
    /**
     * 日常任务调度
     * 默认每天凌晨0点执行
     */
    @Scheduled(cron = "${app.schedules.daily-cron}")
    public void scheduleDailyTask() {
        if (!dailyTaskLock.tryLock()) {
            log.warn("日常任务已在执行中，跳过本次调度");
            return;
        }
        
        String taskId = monitorLogger.generateId();
        String taskName = "日常任务调度";
        long startTime = System.currentTimeMillis();
        Map<String, Object> taskParams = new HashMap<>();
        
        try {
            // 获取昨天的日期作为数据日期
            String dataDate = TaskExecutionService.getYesterdayDateString();
            taskParams.put("dataDate", dataDate);
            taskParams.put("scheduled", true);
            
            // 设置MDC上下文信息
            MdcUtil.setTaskContext(taskId, "daily", dataDate, null);
            MdcUtil.setOperationType("schedule");
            
            // 记录任务开始
            monitorLogger.logTaskStart(taskId, taskName, "daily", taskParams);
            log.info("=== 开始执行日常任务调度 ===");
            log.info("日常任务数据日期: {}", dataDate);
            
            // 检查是否有任务配置
            List<com.vgop.service.config.TaskConfig> configTasks = appConfig.getEnabledDailyTasks();
            if (configTasks.isEmpty()) {
                log.warn("没有配置启用的日常任务，跳过执行");
                Map<String, Object> result = new HashMap<>();
                result.put("status", "skipped");
                result.put("reason", "没有配置启用的日常任务");
                monitorLogger.logTaskComplete(taskId, taskName, "SKIPPED", System.currentTimeMillis() - startTime, result);
                return;
            }
            
            // 获取或创建版本信息
            RevisionTimes revisionTimes = revisionTimesService.getOrCreateRevisionTimes(dataDate, "daily");
            
            // 创建任务执行记录
            TaskExecution execution = taskExecutionService.createExecution(
                    "daily", dataDate, "transfer", revisionTimes.getRevision());
            
            // 添加执行ID到MDC上下文
            MdcUtil.setTaskContext(taskId, "daily", dataDate, String.valueOf(execution.getId()));
            
            // 开始执行
            taskExecutionService.startExecution(execution.getId());
            
            // 执行文件传输（包含数据导出和上传）
            FileTransferService.TransferResult result = fileTransferService.processDailyTransfer(dataDate);
            
            // 记录执行结果
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("exportedFiles", result.getExportedFiles());
            extraInfo.put("exportedRows", result.getExportedRows());
            extraInfo.put("uploadedDatFiles", result.getUploadedDatFiles());
            extraInfo.put("uploadedVerfFiles", result.getUploadedVerfFiles());
            
            taskExecutionService.completeExecution(
                    execution.getId(),
                    result.isSuccess(),
                    result.getExportedFiles() + result.getUploadedDatFiles() + result.getUploadedVerfFiles(),
                    result.getExportedFiles(),
                    result.getFailedInterfaces().size(),
                    result.getErrorMessage(),
                    extraInfo
            );
            
            // 记录监控日志
            Map<String, Object> taskResult = new HashMap<>();
            taskResult.put("success", result.isSuccess());
            taskResult.put("executionId", execution.getId());
            taskResult.put("revision", revisionTimes.getRevision());
            taskResult.put("exportedFiles", result.getExportedFiles());
            taskResult.put("exportedRows", result.getExportedRows());
            taskResult.put("uploadedDatFiles", result.getUploadedDatFiles());
            taskResult.put("uploadedVerfFiles", result.getUploadedVerfFiles());
            
            if (!result.isSuccess()) {
                taskResult.put("errorMessage", result.getErrorMessage());
                taskResult.put("failedInterfaces", result.getFailedInterfaces());
                monitorLogger.logTaskComplete(taskId, taskName, "FAILED", System.currentTimeMillis() - startTime, taskResult);
            } else {
                monitorLogger.logTaskComplete(taskId, taskName, "SUCCESS", System.currentTimeMillis() - startTime, taskResult);
            }
            
            log.info("=== 日常任务调度执行完成 ===");
            
        } catch (Exception e) {
            log.error("日常任务调度执行异常", e);
            monitorLogger.logTaskFailure(taskId, taskName, e, System.currentTimeMillis() - startTime);
        } finally {
            // 清理MDC上下文，避免内存泄漏
            MdcUtil.clearTaskContext();
            MdcUtil.clearOperationType();
            
            dailyTaskLock.unlock();
        }
    }
    
    /**
     * 月度任务调度
     * 默认每月1日凌晨5点执行
     */
    @Scheduled(cron = "${app.schedules.monthly-cron}")
    public void scheduleMonthlyTask() {
        if (!monthlyTaskLock.tryLock()) {
            log.warn("月度任务已在执行中，跳过本次调度");
            return;
        }
        
        String taskId = monitorLogger.generateId();
        String taskName = "月度任务调度";
        long startTime = System.currentTimeMillis();
        Map<String, Object> taskParams = new HashMap<>();
        
        try {
            // 获取上个月第一天的日期作为数据日期
            String dataDate = TaskExecutionService.getLastMonthFirstDayString();
            taskParams.put("dataDate", dataDate);
            taskParams.put("scheduled", true);
            
            // 设置MDC上下文信息
            MdcUtil.setTaskContext(taskId, "monthly", dataDate, null);
            MdcUtil.setOperationType("schedule");
            
            // 记录任务开始
            monitorLogger.logTaskStart(taskId, taskName, "monthly", taskParams);
            log.info("=== 开始执行月度任务调度 ===");
            log.info("月度任务数据日期: {}", dataDate);
            
            // 检查是否有任务配置
            List<com.vgop.service.config.TaskConfig> configTasks = appConfig.getEnabledMonthlyTasks();
            if (configTasks.isEmpty()) {
                log.warn("没有配置启用的月度任务，跳过执行");
                Map<String, Object> result = new HashMap<>();
                result.put("status", "skipped");
                result.put("reason", "没有配置启用的月度任务");
                monitorLogger.logTaskComplete(taskId, taskName, "SKIPPED", System.currentTimeMillis() - startTime, result);
                return;
            }
            
            // 获取或创建版本信息
            RevisionTimes revisionTimes = revisionTimesService.getOrCreateRevisionTimes(dataDate, "monthly");
            
            // 创建任务执行记录
            TaskExecution execution = taskExecutionService.createExecution(
                    "monthly", dataDate, "transfer", revisionTimes.getRevision());
            
            // 添加执行ID到MDC上下文
            MdcUtil.setTaskContext(taskId, "monthly", dataDate, String.valueOf(execution.getId()));
            
            // 开始执行
            taskExecutionService.startExecution(execution.getId());
            
            // 执行文件传输（包含数据导出和上传）
            FileTransferService.TransferResult result = fileTransferService.processMonthlyTransfer(dataDate);
            
            // 记录执行结果
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("exportedFiles", result.getExportedFiles());
            extraInfo.put("exportedRows", result.getExportedRows());
            extraInfo.put("uploadedDatFiles", result.getUploadedDatFiles());
            extraInfo.put("uploadedVerfFiles", result.getUploadedVerfFiles());
            
            taskExecutionService.completeExecution(
                    execution.getId(),
                    result.isSuccess(),
                    result.getExportedFiles() + result.getUploadedDatFiles() + result.getUploadedVerfFiles(),
                    result.getExportedFiles(),
                    result.getFailedInterfaces().size(),
                    result.getErrorMessage(),
                    extraInfo
            );
            
            // 记录监控日志
            Map<String, Object> taskResult = new HashMap<>();
            taskResult.put("success", result.isSuccess());
            taskResult.put("executionId", execution.getId());
            taskResult.put("revision", revisionTimes.getRevision());
            taskResult.put("exportedFiles", result.getExportedFiles());
            taskResult.put("exportedRows", result.getExportedRows());
            taskResult.put("uploadedDatFiles", result.getUploadedDatFiles());
            taskResult.put("uploadedVerfFiles", result.getUploadedVerfFiles());
            
            if (!result.isSuccess()) {
                taskResult.put("errorMessage", result.getErrorMessage());
                taskResult.put("failedInterfaces", result.getFailedInterfaces());
                monitorLogger.logTaskComplete(taskId, taskName, "FAILED", System.currentTimeMillis() - startTime, taskResult);
            } else {
                monitorLogger.logTaskComplete(taskId, taskName, "SUCCESS", System.currentTimeMillis() - startTime, taskResult);
            }
            
            log.info("=== 月度任务调度执行完成 ===");
            
        } catch (Exception e) {
            log.error("月度任务调度执行异常", e);
            monitorLogger.logTaskFailure(taskId, taskName, e, System.currentTimeMillis() - startTime);
        } finally {
            // 清理MDC上下文，避免内存泄漏
            MdcUtil.clearTaskContext();
            MdcUtil.clearOperationType();
            
            monthlyTaskLock.unlock();
        }
    }
    
    /**
     * 手动触发日常任务
     * 
     * @param dataDate 数据日期（格式：yyyyMMdd）
     * @return 执行结果
     */
    public Map<String, Object> triggerDailyTask(String dataDate) {
        if (!dailyTaskLock.tryLock()) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "日常任务已在执行中，请稍后再试");
            return result;
        }
        
        String taskId = monitorLogger.generateId();
        String taskName = "手动触发日常任务";
        long startTime = System.currentTimeMillis();
        Map<String, Object> taskParams = new HashMap<>();
        taskParams.put("dataDate", dataDate);
        taskParams.put("manual", true);
        
        try {
            // 记录任务开始
            monitorLogger.logTaskStart(taskId, taskName, "daily", taskParams);
            log.info("=== 手动触发日常任务 - 数据日期: {} ===", dataDate);
            
            // 获取或创建版本信息
            RevisionTimes revisionTimes = revisionTimesService.getOrCreateRevisionTimes(dataDate, "daily");
            
            // 创建任务执行记录
            TaskExecution execution = taskExecutionService.createExecution(
                    "daily", dataDate, "transfer", revisionTimes.getRevision());
            
            // 开始执行
            taskExecutionService.startExecution(execution.getId());
            
            // 执行文件传输
            FileTransferService.TransferResult result = fileTransferService.processDailyTransfer(dataDate);
            
            // 记录执行结果
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("exportedFiles", result.getExportedFiles());
            extraInfo.put("exportedRows", result.getExportedRows());
            extraInfo.put("uploadedDatFiles", result.getUploadedDatFiles());
            extraInfo.put("uploadedVerfFiles", result.getUploadedVerfFiles());
            extraInfo.put("manual", true);
            extraInfo.put("triggerTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            taskExecutionService.completeExecution(
                    execution.getId(),
                    result.isSuccess(),
                    result.getExportedFiles() + result.getUploadedDatFiles() + result.getUploadedVerfFiles(),
                    result.getExportedFiles(),
                    result.getFailedInterfaces().size(),
                    result.getErrorMessage(),
                    extraInfo
            );
            
            // 返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("dataDate", dataDate);
            response.put("taskType", "daily");
            response.put("executionId", execution.getId());
            response.put("revision", revisionTimes.getRevision());
            response.put("exportedFiles", result.getExportedFiles());
            response.put("exportedRows", result.getExportedRows());
            response.put("uploadedDatFiles", result.getUploadedDatFiles());
            response.put("uploadedVerfFiles", result.getUploadedVerfFiles());
            
            if (!result.isSuccess()) {
                response.put("errorMessage", result.getErrorMessage());
                response.put("failedInterfaces", result.getFailedInterfaces());
                monitorLogger.logTaskComplete(taskId, taskName, "FAILED", System.currentTimeMillis() - startTime, response);
            } else {
                monitorLogger.logTaskComplete(taskId, taskName, "SUCCESS", System.currentTimeMillis() - startTime, response);
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("手动触发日常任务异常", e);
            monitorLogger.logTaskFailure(taskId, taskName, e, System.currentTimeMillis() - startTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "执行异常: " + e.getMessage());
            return result;
        } finally {
            dailyTaskLock.unlock();
        }
    }
    
    /**
     * 手动触发月度任务
     * 
     * @param dataDate 数据日期（格式：yyyyMMdd）
     * @return 执行结果
     */
    public Map<String, Object> triggerMonthlyTask(String dataDate) {
        if (!monthlyTaskLock.tryLock()) {
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "月度任务已在执行中，请稍后再试");
            return result;
        }
        
        String taskId = monitorLogger.generateId();
        String taskName = "手动触发月度任务";
        long startTime = System.currentTimeMillis();
        Map<String, Object> taskParams = new HashMap<>();
        taskParams.put("dataDate", dataDate);
        taskParams.put("manual", true);
        
        try {
            // 记录任务开始
            monitorLogger.logTaskStart(taskId, taskName, "monthly", taskParams);
            log.info("=== 手动触发月度任务 - 数据日期: {} ===", dataDate);
            
            // 获取或创建版本信息
            RevisionTimes revisionTimes = revisionTimesService.getOrCreateRevisionTimes(dataDate, "monthly");
            
            // 创建任务执行记录
            TaskExecution execution = taskExecutionService.createExecution(
                    "monthly", dataDate, "transfer", revisionTimes.getRevision());
            
            // 开始执行
            taskExecutionService.startExecution(execution.getId());
            
            // 执行文件传输
            FileTransferService.TransferResult result = fileTransferService.processMonthlyTransfer(dataDate);
            
            // 记录执行结果
            Map<String, Object> extraInfo = new HashMap<>();
            extraInfo.put("exportedFiles", result.getExportedFiles());
            extraInfo.put("exportedRows", result.getExportedRows());
            extraInfo.put("uploadedDatFiles", result.getUploadedDatFiles());
            extraInfo.put("uploadedVerfFiles", result.getUploadedVerfFiles());
            extraInfo.put("manual", true);
            extraInfo.put("triggerTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            
            taskExecutionService.completeExecution(
                    execution.getId(),
                    result.isSuccess(),
                    result.getExportedFiles() + result.getUploadedDatFiles() + result.getUploadedVerfFiles(),
                    result.getExportedFiles(),
                    result.getFailedInterfaces().size(),
                    result.getErrorMessage(),
                    extraInfo
            );
            
            // 返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("dataDate", dataDate);
            response.put("taskType", "monthly");
            response.put("executionId", execution.getId());
            response.put("revision", revisionTimes.getRevision());
            response.put("exportedFiles", result.getExportedFiles());
            response.put("exportedRows", result.getExportedRows());
            response.put("uploadedDatFiles", result.getUploadedDatFiles());
            response.put("uploadedVerfFiles", result.getUploadedVerfFiles());
            
            if (!result.isSuccess()) {
                response.put("errorMessage", result.getErrorMessage());
                response.put("failedInterfaces", result.getFailedInterfaces());
                monitorLogger.logTaskComplete(taskId, taskName, "FAILED", System.currentTimeMillis() - startTime, response);
            } else {
                monitorLogger.logTaskComplete(taskId, taskName, "SUCCESS", System.currentTimeMillis() - startTime, response);
            }
            
            return response;
            
        } catch (Exception e) {
            log.error("手动触发月度任务异常", e);
            monitorLogger.logTaskFailure(taskId, taskName, e, System.currentTimeMillis() - startTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "执行异常: " + e.getMessage());
            return result;
        } finally {
            monthlyTaskLock.unlock();
        }
    }
} 