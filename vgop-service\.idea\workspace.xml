<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="702d0c1d-ef50-464a-9690-3092f1fda764" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/src/main/java/com/vgop/service/scheduler/VgopTaskScheduler.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/vgop/service/scheduler/VgopTaskScheduler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/DataExportService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/DataExportService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/UnloadExecutorService.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/vgop/service/service/UnloadExecutorService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/logback-spring.xml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/logback-spring.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2ztpm4eb265ByBUymMZxEq80so6" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.vgop-service [clean].executor": "Run",
    "Maven.vgop-service [package].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.VgopServiceApplication.executor": "Debug",
    "git-widget-placeholder": "master",
    "last_opened_file_path": "C:/workspaces/hdh/vgop-vli/vgop-service",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "Artifacts",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.2",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="VgopServiceApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="vgop-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.vgop.service.VgopServiceApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="702d0c1d-ef50-464a-9690-3092f1fda764" name="Changes" comment="" />
      <created>1752560378271</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752560378271</updated>
      <workItem from="1752560379392" duration="7777000" />
      <workItem from="1752630215495" duration="2703000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>