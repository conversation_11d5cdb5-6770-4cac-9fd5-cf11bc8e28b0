<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProperty scope="context" name="LOG_ROOT" source="app.base-path.log-root" defaultValue="./logs"/>
    <springProperty scope="context" name="APP_NAME" source="spring.application.name" defaultValue="vgop-service"/>
    <springProperty scope="context" name="LOG_LEVEL" source="logging.level.com.vgop.service" defaultValue="INFO"/>

    <property name="CONSOLE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{threadName:-}%thread] %-5level %logger{36} [%X{interfaceId:-}%X{batchOperation:-}%X{taskId:-}] [%X{operationType:-}] - %msg%n"/>
    <property name="FILE_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{threadName:-}%thread] %-5level %logger{36} [%X{interfaceId:-}%X{batchOperation:-}%X{taskId:-}|%X{dataDate:-}|%X{revision:-}|%X{taskType:-}|%X{executionId:-}] [%X{operationType:-}] - %msg%n"/>
    <property name="JSON_LOG_PATTERN" value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{threadName:-}%thread] %-5level %logger{36} [%X{interfaceId:-}%X{batchOperation:-}%X{taskId:-}] [%X{operationType:-}] - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 应用日志文件 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_ROOT}/${APP_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_ROOT}/history/${APP_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>3GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 错误日志文件 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_ROOT}/${APP_NAME}-error.log</file>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_ROOT}/history/${APP_NAME}-error-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- JSON格式日志文件（用于ELK分析） -->
    <appender name="JSON_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_ROOT}/${APP_NAME}-json.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_ROOT}/history/${APP_NAME}-json-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <includeContext>true</includeContext>
            <includeMdc>true</includeMdc>
            <customFields>{"application":"${APP_NAME}"}</customFields>
        </encoder>
    </appender>

    <!-- 任务执行日志文件 -->
    <appender name="TASK_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_ROOT}/${APP_NAME}-task.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_ROOT}/history/${APP_NAME}-task-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>${FILE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 任务执行日志记录器 -->
    <logger name="com.vgop.service.scheduler" level="${LOG_LEVEL}" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APP_FILE" />
        <appender-ref ref="ERROR_FILE" />
        <appender-ref ref="JSON_FILE" />
        <appender-ref ref="TASK_FILE" />
    </logger>

    <!-- 数据导出日志记录器 -->
    <logger name="com.vgop.service.service.DataExportService" level="${LOG_LEVEL}" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APP_FILE" />
        <appender-ref ref="ERROR_FILE" />
        <appender-ref ref="JSON_FILE" />
        <appender-ref ref="TASK_FILE" />
    </logger>

    <!-- 文件传输日志记录器 -->
    <logger name="com.vgop.service.service.FileTransferService" level="${LOG_LEVEL}" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APP_FILE" />
        <appender-ref ref="ERROR_FILE" />
        <appender-ref ref="JSON_FILE" />
        <appender-ref ref="TASK_FILE" />
    </logger>

    <!-- SFTP日志记录器 -->
    <logger name="com.vgop.service.sftp" level="${LOG_LEVEL}" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APP_FILE" />
        <appender-ref ref="ERROR_FILE" />
        <appender-ref ref="JSON_FILE" />
    </logger>

    <!-- 根日志记录器 -->
    <root level="${LOG_LEVEL}">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APP_FILE" />
        <appender-ref ref="ERROR_FILE" />
        <appender-ref ref="JSON_FILE" />
    </root>
</configuration> 