package com.vgop.service.service;

import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.dto.FileTransferRequest;
import com.vgop.service.dto.FileTransferResponse;
import com.vgop.service.exception.VgopException;
import com.vgop.service.sftp.SftpUtil;
import com.vgop.service.util.DateTimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * DMZ文件中转服务
 * 负责接收内网文件清单通知，在DMZ本地查找文件并转发到下游系统
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DmzFileTransferService {
    
    private final VgopAppConfig appConfig;
    
    /**
     * 处理文件中转请求
     * 
     * @param request 文件传输请求
     * @return 传输结果
     */
    public FileTransferResponse processFileTransfer(FileTransferRequest request) {
        log.info("开始处理文件中转请求 - 任务ID: {}, 接口ID: {}, 文件数: {}", 
                 request.getTaskId(), request.getInterfaceId(), 
                 request.getFileList() != null ? request.getFileList().size() : 0);
        
        LocalDateTime startTime = LocalDateTime.now();
        
        FileTransferResponse.FileTransferResponseBuilder responseBuilder = FileTransferResponse.builder()
                .taskId(request.getTaskId())
                .responseTime(LocalDateTime.now())
                .startTime(startTime)
                .totalFiles(request.getFileList() != null ? request.getFileList().size() : 0)
                .successFiles(0)
                .failedFiles(0)
                .notFoundFiles(0)
                .successFileList(new ArrayList<>())
                .failedFileList(new ArrayList<>());
        
        try {
            // 验证DMZ配置
            if (appConfig.getDmz() == null) {
                throw new VgopException("DMZ配置未设置");
            }
            
            // 获取启用的下游SFTP服务器列表
            List<VgopAppConfig.DmzConfig.DownstreamSftpConfig> enabledServers = getEnabledSftpServers();
            if (enabledServers.isEmpty()) {
                throw new VgopException("没有启用的下游SFTP服务器");
            }
            
            // 验证文件清单
            if (request.getFileList() == null || request.getFileList().isEmpty()) {
                log.warn("文件清单为空，无需处理");
                return responseBuilder
                        .status("SUCCESS")
                        .endTime(LocalDateTime.now())
                        .message("文件清单为空，无需处理")
                        .build();
            }
            
            // 文件筛选：只处理.dat和.verf文件（与脚本中的mirror -I *.dat -I *.verf保持一致）
            List<FileTransferRequest.FileInfo> filteredFiles = request.getFileList().stream()
                    .filter(file -> {
                        String fileName = file.getFileName();
                        return fileName != null && (fileName.endsWith(".dat") || fileName.endsWith(".verf"));
                    })
                    .collect(java.util.stream.Collectors.toList());
            
            if (filteredFiles.isEmpty()) {
                log.warn("没有找到需要传输的.dat或.verf文件");
                return responseBuilder
                        .status("SUCCESS")
                        .endTime(LocalDateTime.now())
                        .message("没有找到需要传输的.dat或.verf文件")
                        .build();
            }
            
            log.info("文件筛选完成，原始文件数: {}, 筛选后文件数: {}", 
                     request.getFileList().size(), filteredFiles.size());
            
            // 处理每个文件到所有启用的服务器
            List<FileTransferResponse.FileTransferResult> successResults = new ArrayList<>();
            List<FileTransferResponse.FileTransferResult> failedResults = new ArrayList<>();
            
            for (FileTransferRequest.FileInfo fileInfo : filteredFiles) {
                List<FileTransferResponse.FileTransferResult> fileResults = transferToMultipleServers(fileInfo, request, enabledServers);
                
                // 统计结果：如果至少有一个服务器传输成功，则认为该文件传输成功
                boolean hasSuccess = fileResults.stream().anyMatch(r -> "SUCCESS".equals(r.getStatus()));
                
                if (hasSuccess) {
                    successResults.addAll(fileResults.stream()
                            .filter(r -> "SUCCESS".equals(r.getStatus()))
                            .collect(java.util.stream.Collectors.toList()));
                } else {
                    failedResults.addAll(fileResults);
                }
            }
            
            // 计算统计信息
            int successCount = (int) successResults.stream()
                    .map(FileTransferResponse.FileTransferResult::getFileName)
                    .distinct()
                    .count();
            int failedCount = filteredFiles.size() - successCount;
            int notFoundCount = (int) failedResults.stream()
                    .filter(r -> "NOT_FOUND".equals(r.getStatus()))
                    .map(FileTransferResponse.FileTransferResult::getFileName)
                    .distinct()
                    .count();
            
            // 确定整体状态
            String overallStatus;
            if (successCount == filteredFiles.size()) {
                overallStatus = "SUCCESS";
            } else if (successCount == 0) {
                overallStatus = "FAILED";
            } else {
                overallStatus = "PARTIAL";
            }
            
            LocalDateTime endTime = LocalDateTime.now();
            
            String message = String.format("文件中转处理完成 - 总计: %d, 成功: %d, 失败: %d, 未找到: %d, 目标服务器: %d", 
                                          filteredFiles.size(), successCount, failedCount, notFoundCount, enabledServers.size());
            
            return responseBuilder
                    .status(overallStatus)
                    .endTime(endTime)
                    .successFiles(successCount)
                    .failedFiles(failedCount)
                    .notFoundFiles(notFoundCount)
                    .successFileList(successResults)
                    .failedFileList(failedResults)
                    .message(message)
                    .build();
                    
        } catch (Exception e) {
            log.error("文件中转处理异常 - 任务ID: {}", request.getTaskId(), e);
            
            return responseBuilder
                    .status("FAILED")
                    .endTime(LocalDateTime.now())
                    .errorDetail(e.getMessage())
                    .message("文件中转处理失败: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 传输单个文件到指定服务器
     * 
     * @param fileInfo 文件信息
     * @param request 原始请求
     * @param sftpConfig SFTP服务器配置
     * @return 传输结果
     */
    private FileTransferResponse.FileTransferResult transferSingleFileToServer(
            FileTransferRequest.FileInfo fileInfo, 
            FileTransferRequest request,
            VgopAppConfig.DmzConfig.DownstreamSftpConfig sftpConfig) {
        
        LocalDateTime transferStartTime = LocalDateTime.now();
        
        FileTransferResponse.FileTransferResult.FileTransferResultBuilder resultBuilder = 
                FileTransferResponse.FileTransferResult.builder()
                        .fileName(fileInfo.getFileName())
                        .transferStartTime(transferStartTime);
        
        try {
            // 1. 构建DMZ本地文件路径
            String dmzLocalPath = buildDmzLocalFilePath(fileInfo, request);
            resultBuilder.localFilePath(dmzLocalPath);
            
            // 2. 检查文件是否存在
            File localFile = new File(dmzLocalPath);
            if (!localFile.exists()) {
                log.warn("DMZ本地文件不存在: {}", dmzLocalPath);
                return resultBuilder
                        .status("NOT_FOUND")
                        .transferEndTime(LocalDateTime.now())
                        .errorReason("DMZ本地文件不存在: " + dmzLocalPath)
                        .build();
            }
            
            // 3. 验证文件大小（可选）
            long actualFileSize = localFile.length();
            if (fileInfo.getFileSize() != null && !fileInfo.getFileSize().equals(actualFileSize)) {
                log.warn("文件大小不匹配 - 期望: {}, 实际: {}, 文件: {}", 
                         fileInfo.getFileSize(), actualFileSize, dmzLocalPath);
            }
            
            // 4. 构建下游SFTP路径
            String remoteFilePath = buildDownstreamRemoteFilePath(fileInfo, request, sftpConfig);
            resultBuilder.remoteFilePath(remoteFilePath);
            
            // 5. 执行SFTP传输
            uploadWithRetry(sftpConfig, dmzLocalPath, remoteFilePath);
            
            LocalDateTime transferEndTime = LocalDateTime.now();
            long duration = java.time.Duration.between(transferStartTime, transferEndTime).toMillis();
            
            log.info("文件传输成功: {} -> {} (服务器: {}), 耗时: {}ms", 
                     dmzLocalPath, remoteFilePath, sftpConfig.getName(), duration);
            
            return resultBuilder
                    .status("SUCCESS")
                    .transferEndTime(transferEndTime)
                    .transferDuration(duration)
                    .fileSize(actualFileSize)
                    .build();
                    
        } catch (Exception e) {
            LocalDateTime transferEndTime = LocalDateTime.now();
            long duration = java.time.Duration.between(transferStartTime, transferEndTime).toMillis();
            
            log.error("文件传输失败: {} (服务器: {})", fileInfo.getFileName(), sftpConfig.getName(), e);
            
            return resultBuilder
                    .status("FAILED")
                    .transferEndTime(transferEndTime)
                    .transferDuration(duration)
                    .errorReason("传输到服务器 " + sftpConfig.getName() + " 失败: " + e.getMessage())
                    .build();
        }
    }
    

    
    /**
     * 构建DMZ本地文件路径
     * 参照脚本中的路径结构：
     * - 日统计：daydatapath=${ImagePath}"/VGOPdata/datafile/"${BeforeDay}"/day/"
     * - 月统计：monthdatapath=${ImagePath}"/VGOPdata/datafile/"${BeforeMonth}"/month/"
     * 
     * 注意：传入的request.getDateId()已经在上游VgopTaskScheduler中进行了日期转换
     * （日统计任务已转换为前一天日期，月统计任务已转换为前一月日期）
     * 
     * @param fileInfo 文件信息
     * @param request 请求信息
     * @return DMZ本地文件路径
     */
    private String buildDmzLocalFilePath(FileTransferRequest.FileInfo fileInfo, FileTransferRequest request) {
        String dmzLocalDir = appConfig.getDmz().getLocalFileDirectory();
        if (!dmzLocalDir.endsWith("/")) {
            dmzLocalDir += "/";
        }
        
        log.debug("构建DMZ本地文件路径 - 基础目录: {}, 任务类型: {}, 日期ID: {}, 文件名: {}", 
                 dmzLocalDir, request.getTaskType(), request.getDateId(), fileInfo.getFileName());
        
        // 根据任务类型确定路径结构
        if ("daily".equals(request.getTaskType())) {
            // 日统计任务：/VGOPdata/datafile/{dateId}/day/
            // dateId已经是前一天日期
            dmzLocalDir += request.getDateId() + "/day/";
            log.debug("日统计任务路径构建 - 日期ID: {}, 完整路径: {}", 
                     request.getDateId(), dmzLocalDir);
        } else if ("monthly".equals(request.getTaskType())) {
            // 月统计任务：/VGOPdata/datafile/{dateId}/month/
            // dateId已经是前一月日期
            dmzLocalDir += request.getDateId() + "/month/";
            log.debug("月统计任务路径构建 - 日期ID: {}, 完整路径: {}", 
                     request.getDateId(), dmzLocalDir);
        } else {
            // 默认使用日统计路径结构
            log.warn("未知的任务类型: {}，使用默认日统计路径结构", request.getTaskType());
            dmzLocalDir += request.getDateId() + "/day/";
            log.debug("默认日统计任务路径构建 - 日期ID: {}, 完整路径: {}", 
                     request.getDateId(), dmzLocalDir);
        }
        
        String fullPath = dmzLocalDir + fileInfo.getFileName();
        log.info("DMZ本地文件路径构建完成: {}", fullPath);
        return fullPath;
    }
    
    /**
     * 构建下游SFTP远程文件路径
     * 参照脚本中的PATH_Target="/data/yikaduohao/"和mirror -R命令
     * 
     * Shell脚本逻辑：
     * - 本地路径：${ImagePath}/VGOPdata/datafile/${BeforeDay}/day/
     * - 远程目标：/data/yikaduohao/
     * - mirror -R 会保持目录结构，最终路径：/data/yikaduohao/20250620/day/filename.dat
     * 
     * @param fileInfo 文件信息
     * @param request 请求信息
     * @param sftpConfig SFTP服务器配置
     * @return 下游SFTP远程文件路径
     */
    private String buildDownstreamRemoteFilePath(FileTransferRequest.FileInfo fileInfo, 
                                                FileTransferRequest request,
                                                VgopAppConfig.DmzConfig.DownstreamSftpConfig sftpConfig) {
        String remoteBasePath = sftpConfig.getRemoteBasePath();
        if (!remoteBasePath.endsWith("/")) {
            remoteBasePath += "/";
        }
        
        // 根据任务类型构建远程路径，模拟mirror -R命令的行为
        // 保持与本地目录结构一致的远程目录结构
        String remotePath;
        if ("daily".equals(request.getTaskType())) {
            // 日统计任务：/data/yikaduohao/20250620/day/filename.dat
            remotePath = remoteBasePath + request.getDateId() + "/day/" + fileInfo.getFileName();
        } else if ("monthly".equals(request.getTaskType())) {
            // 月统计任务：/data/yikaduohao/202506/month/filename.dat
            remotePath = remoteBasePath + request.getDateId() + "/month/" + fileInfo.getFileName();
        } else {
            // 默认使用日统计路径结构
            log.warn("未知的任务类型: {}，使用默认日统计路径结构", request.getTaskType());
            remotePath = remoteBasePath + request.getDateId() + "/day/" + fileInfo.getFileName();
        }
        
        log.debug("构建下游SFTP远程路径 - 服务器: {}, 任务类型: {}, 日期ID: {}, 文件名: {}, 远程路径: {}", 
                 sftpConfig.getName(), request.getTaskType(), request.getDateId(), fileInfo.getFileName(), remotePath);
        
        return remotePath;
    }
    
    /**
     * 带重试的SFTP上传
     * 
     * @param sftpConfig SFTP配置
     * @param localFilePath 本地文件路径
     * @param remoteFilePath 远程文件路径
     */
    private void uploadWithRetry(VgopAppConfig.DmzConfig.DownstreamSftpConfig sftpConfig, 
                                String localFilePath, String remoteFilePath) throws VgopException {
        int maxRetries = sftpConfig.getRetryTimes();
        int attempt = 0;
        Exception lastException = null;
        
        String serverName = sftpConfig.getName() != null ? sftpConfig.getName() : sftpConfig.getHost();
        
        // 转换为SftpConfig格式（复用现有的SftpUtil）
        VgopAppConfig.SftpConfig adaptedConfig = new VgopAppConfig.SftpConfig();
        adaptedConfig.setHost(sftpConfig.getHost());
        adaptedConfig.setPort(sftpConfig.getPort());
        adaptedConfig.setUsername(sftpConfig.getUsername());
        adaptedConfig.setPassword(sftpConfig.getPassword());
        adaptedConfig.setConnectionTimeout(sftpConfig.getConnectionTimeout());
        
        while (attempt <= maxRetries) {
            try {
                SftpUtil.upload(adaptedConfig, localFilePath, remoteFilePath);
                return; // 成功，退出方法
                
            } catch (VgopException e) {
                lastException = e;
                attempt++;
                log.error("下游SFTP上传失败 (服务器: {}, 尝试 {}/{}): {} -> {}", 
                         serverName, attempt, maxRetries + 1, localFilePath, remoteFilePath, e);
                
                if (attempt <= maxRetries) {
                    try {
                        Thread.sleep(1000 * attempt); // 等待后重试
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new VgopException("SFTP上传在重试等待中被中断", ie);
                    }
                }
            }
        }
        
        throw new VgopException("下游SFTP上传最终失败，已达最大重试次数 (服务器: " + serverName + "): " + localFilePath, lastException);
    }
    
    /**
     * 计算文件MD5值
     * 
     * @param filePath 文件路径
     * @return MD5值
     */
    private String calculateMD5(String filePath) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] fileBytes = Files.readAllBytes(Paths.get(filePath));
            byte[] hashBytes = md.digest(fileBytes);
            
            StringBuilder sb = new StringBuilder();
            for (byte b : hashBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
            
        } catch (Exception e) {
            log.warn("计算文件MD5失败: {}", filePath, e);
            return null;
        }
    }

    /**
     * 获取启用的下游SFTP服务器列表
     * 
     * @return 启用的SFTP服务器配置列表
     */
    private List<VgopAppConfig.DmzConfig.DownstreamSftpConfig> getEnabledSftpServers() {
        if (appConfig.getDmz().getDownstreamSftpList() == null || appConfig.getDmz().getDownstreamSftpList().isEmpty()) {
            return new ArrayList<>();
        }
        
        return appConfig.getDmz().getDownstreamSftpList().stream()
                .filter(VgopAppConfig.DmzConfig.DownstreamSftpConfig::isEnabled)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 将文件传输到多个服务器
     * 
     * @param fileInfo 文件信息
     * @param request 原始请求
     * @param sftpServers SFTP服务器列表
     * @return 传输结果列表
     */
    private List<FileTransferResponse.FileTransferResult> transferToMultipleServers(
            FileTransferRequest.FileInfo fileInfo, 
            FileTransferRequest request,
            List<VgopAppConfig.DmzConfig.DownstreamSftpConfig> sftpServers) {
        
        List<FileTransferResponse.FileTransferResult> results = new ArrayList<>();
        
        for (VgopAppConfig.DmzConfig.DownstreamSftpConfig server : sftpServers) {
            try {
                FileTransferResponse.FileTransferResult result = transferSingleFileToServer(fileInfo, request, server);
                results.add(result);
            } catch (Exception e) {
                log.error("传输文件到服务器 {} 失败: {}", server.getName(), fileInfo.getFileName(), e);
                
                FileTransferResponse.FileTransferResult failedResult = FileTransferResponse.FileTransferResult.builder()
                        .fileName(fileInfo.getFileName())
                        .status("FAILED")
                        .transferStartTime(LocalDateTime.now())
                        .transferEndTime(LocalDateTime.now())
                        .errorReason("传输到服务器 " + server.getName() + " 失败: " + e.getMessage())
                        .build();
                results.add(failedResult);
            }
        }
        
        return results;
    }
} 