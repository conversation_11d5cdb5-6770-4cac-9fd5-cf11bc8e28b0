package com.vgop.service.util;

import org.slf4j.MDC;
import java.util.Map;
import java.util.concurrent.Callable;

/**
 * MDC（Mapped Diagnostic Context）工具类
 * 用于统一管理日志上下文，确保在多线程环境下正确传递和清理MDC信息
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
public class MdcUtil {
    
    // MDC键常量
    public static final String INTERFACE_ID = "interfaceId";
    public static final String DATA_DATE = "dataDate";
    public static final String REVISION = "revision";
    public static final String BATCH_OPERATION = "batchOperation";
    public static final String OPERATION_TYPE = "operationType";
    public static final String THREAD_NAME = "threadName";
    public static final String TASK_ID = "taskId";
    public static final String TASK_TYPE = "taskType";
    public static final String EXECUTION_ID = "executionId";
    
    /**
     * 设置接口相关的MDC上下文
     * 
     * @param interfaceId 接口ID
     * @param dataDate 数据日期
     * @param revision 版本号
     */
    public static void setInterfaceContext(String interfaceId, String dataDate, Integer revision) {
        MDC.put(INTERFACE_ID, interfaceId);
        if (dataDate != null) {
            MDC.put(DATA_DATE, dataDate);
        }
        if (revision != null) {
            MDC.put(REVISION, String.valueOf(revision));
        }
        MDC.put(THREAD_NAME, Thread.currentThread().getName());
    }
    
    /**
     * 设置批量操作的MDC上下文
     * 
     * @param operation 批量操作名称
     * @param dataDate 数据日期
     */
    public static void setBatchContext(String operation, String dataDate) {
        MDC.put(BATCH_OPERATION, operation);
        if (dataDate != null) {
            MDC.put(DATA_DATE, dataDate);
        }
        MDC.put(THREAD_NAME, Thread.currentThread().getName());
    }
    
    /**
     * 设置任务调度的MDC上下文
     * 
     * @param taskId 任务ID
     * @param taskType 任务类型（daily/monthly）
     * @param dataDate 数据日期
     * @param executionId 执行ID（可选）
     */
    public static void setTaskContext(String taskId, String taskType, String dataDate, String executionId) {
        MDC.put(TASK_ID, taskId);
        MDC.put(TASK_TYPE, taskType);
        if (dataDate != null) {
            MDC.put(DATA_DATE, dataDate);
        }
        if (executionId != null) {
            MDC.put(EXECUTION_ID, executionId);
        }
        MDC.put(THREAD_NAME, Thread.currentThread().getName());
    }
    
    /**
     * 设置操作类型
     * 
     * @param operationType 操作类型（如：export, validate, transfer等）
     */
    public static void setOperationType(String operationType) {
        MDC.put(OPERATION_TYPE, operationType);
    }
    
    /**
     * 清理接口相关的MDC上下文
     */
    public static void clearInterfaceContext() {
        MDC.remove(INTERFACE_ID);
        MDC.remove(DATA_DATE);
        MDC.remove(REVISION);
        MDC.remove(THREAD_NAME);
    }
    
    /**
     * 清理批量操作的MDC上下文
     */
    public static void clearBatchContext() {
        MDC.remove(BATCH_OPERATION);
        MDC.remove(DATA_DATE);
        MDC.remove(THREAD_NAME);
    }
    
    /**
     * 清理任务调度的MDC上下文
     */
    public static void clearTaskContext() {
        MDC.remove(TASK_ID);
        MDC.remove(TASK_TYPE);
        MDC.remove(DATA_DATE);
        MDC.remove(EXECUTION_ID);
        MDC.remove(THREAD_NAME);
    }
    
    /**
     * 清理操作类型
     */
    public static void clearOperationType() {
        MDC.remove(OPERATION_TYPE);
    }
    
    /**
     * 完全清理所有MDC上下文
     */
    public static void clearAll() {
        MDC.clear();
    }
    
    /**
     * 获取当前MDC上下文的拷贝
     * 用于在新线程中传递上下文
     * 
     * @return MDC上下文拷贝
     */
    public static Map<String, String> getCopyOfContextMap() {
        return MDC.getCopyOfContextMap();
    }
    
    /**
     * 设置MDC上下文
     * 用于在新线程中恢复上下文
     * 
     * @param contextMap MDC上下文
     */
    public static void setContextMap(Map<String, String> contextMap) {
        if (contextMap != null) {
            MDC.setContextMap(contextMap);
        }
    }
    
    /**
     * 在指定的上下文中执行任务
     * 自动管理MDC上下文的设置和清理
     * 
     * @param interfaceId 接口ID
     * @param dataDate 数据日期
     * @param revision 版本号
     * @param task 要执行的任务
     * @param <T> 返回值类型
     * @return 任务执行结果
     * @throws Exception 任务执行异常
     */
    public static <T> T executeWithContext(String interfaceId, String dataDate, Integer revision, 
                                          Callable<T> task) throws Exception {
        setInterfaceContext(interfaceId, dataDate, revision);
        try {
            return task.call();
        } finally {
            clearInterfaceContext();
        }
    }
    
    /**
     * 在指定的批量操作上下文中执行任务
     * 自动管理MDC上下文的设置和清理
     * 
     * @param operation 批量操作名称
     * @param dataDate 数据日期
     * @param task 要执行的任务
     * @param <T> 返回值类型
     * @return 任务执行结果
     * @throws Exception 任务执行异常
     */
    public static <T> T executeWithBatchContext(String operation, String dataDate, 
                                               Callable<T> task) throws Exception {
        setBatchContext(operation, dataDate);
        try {
            return task.call();
        } finally {
            clearBatchContext();
        }
    }
    
    /**
     * 创建一个包装了MDC上下文的Runnable
     * 用于在新线程中执行任务时保持当前的MDC上下文
     * 
     * @param task 要执行的任务
     * @return 包装了MDC上下文的Runnable
     */
    public static Runnable wrapWithMdcContext(Runnable task) {
        Map<String, String> contextMap = getCopyOfContextMap();
        return () -> {
            Map<String, String> originalContext = getCopyOfContextMap();
            try {
                setContextMap(contextMap);
                task.run();
            } finally {
                setContextMap(originalContext);
            }
        };
    }
    
    /**
     * 创建一个包装了MDC上下文的Callable
     * 用于在新线程中执行任务时保持当前的MDC上下文
     * 
     * @param task 要执行的任务
     * @param <T> 返回值类型
     * @return 包装了MDC上下文的Callable
     */
    public static <T> Callable<T> wrapWithMdcContext(Callable<T> task) {
        Map<String, String> contextMap = getCopyOfContextMap();
        return () -> {
            Map<String, String> originalContext = getCopyOfContextMap();
            try {
                setContextMap(contextMap);
                return task.call();
            } finally {
                setContextMap(originalContext);
            }
        };
    }
} 