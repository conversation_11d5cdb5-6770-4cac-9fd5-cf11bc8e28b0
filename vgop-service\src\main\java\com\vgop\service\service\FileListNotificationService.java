package com.vgop.service.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.vgop.service.config.VgopAppConfig;
import com.vgop.service.dto.ApiResponse;
import com.vgop.service.dto.FileTransferRequest;
import com.vgop.service.dto.FileTransferResponse;
import com.vgop.service.exception.VgopException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

/**
 * 文件清单通知服务
 * 内网端负责向DMZ发送文件清单通知的服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileListNotificationService {
    
    private final VgopAppConfig appConfig;
    private final ObjectMapper objectMapper;
    
    /**
     * 向DMZ发送文件清单通知
     * 
     * @param taskId 任务ID
     * @param dateId 数据日期
     * @param interfaceId 接口ID
     * @param taskType 任务类型
     * @param revision 版本号
     * @param outputDir 输出目录（包含生成的.dat和.verf文件）
     * @return 通知结果
     */
    public FileTransferResponse notifyDmzFileTransfer(String taskId, String dateId, String interfaceId, 
                                                     String taskType, String revision, String outputDir) {
        log.info("准备向DMZ发送文件清单通知 - 任务ID: {}, 接口ID: {}, 版本号: {}, 输出目录: {}", 
                taskId, interfaceId, revision, outputDir);
        
        try {
            // 1. 验证DMZ配置
            if (appConfig.getDmz() == null) {
                throw new VgopException("DMZ配置未设置");
            }
            
            // 2. 扫描输出目录，构建文件清单
            List<FileTransferRequest.FileInfo> fileList = scanOutputDirectory(outputDir, interfaceId, revision);
            
            if (fileList.isEmpty()) {
                log.warn("输出目录中没有找到符合条件的文件: {}", outputDir);
                return createEmptyResponse(taskId, "输出目录中没有找到符合条件的文件");
            }
            
            // 3. 构建文件传输请求
            FileTransferRequest request = FileTransferRequest.builder()
                    .taskId(taskId)
                    .dateId(dateId)
                    .interfaceId(interfaceId)
                    .taskType(taskType)
                    .requestTime(LocalDateTime.now())
                    .fileList(fileList)
                    .remarks("VGOP内网任务文件中转请求")
                    .build();
            
            // 4. 发送HTTP请求到DMZ
            FileTransferResponse response = sendNotificationToDmz(request);
            
            log.info("DMZ文件清单通知完成 - 任务ID: {}, 状态: {}, 成功文件: {}, 失败文件: {}", 
                     taskId, response.getStatus(), response.getSuccessFiles(), response.getFailedFiles());
            
            return response;
            
        } catch (Exception e) {
            log.error("向DMZ发送文件清单通知失败 - 任务ID: {}", taskId, e);
            return createErrorResponse(taskId, "文件清单通知失败: " + e.getMessage());
        }
    }
    
    /**
     * 扫描输出目录，构建文件清单
     * 
     * @param outputDir 输出目录
     * @param interfaceId 接口ID（用于过滤文件）
     * @param revision 版本号（用于过滤特定版本的文件）
     * @return 文件信息列表
     */
    private List<FileTransferRequest.FileInfo> scanOutputDirectory(String outputDir, String interfaceId, String revision) {
        List<FileTransferRequest.FileInfo> fileList = new ArrayList<>();
        
        if (outputDir == null) {
            return fileList;
        }
        
        File directory = new File(outputDir);
        if (!directory.exists() || !directory.isDirectory()) {
            log.warn("输出目录不存在或不是目录: {}", outputDir);
            return fileList;
        }
        
        // 获取符合条件的.dat和.verf文件
        File[] files = directory.listFiles((dir, name) -> {
            if (!name.endsWith(".dat") && !name.endsWith(".verf")) {
                return false;
            }
            
            // 检查文件名是否包含当前任务的接口ID和版本号
            // 文件名格式：{prefix}_{dataDate}_{interfaceId}_{revision}_{fileNum}.dat/.verf
            String[] nameParts = name.split("_");
            if (nameParts.length >= 5) {  // 确保至少有5个部分，包括版本号
                String fileInterfaceId = nameParts[3];
                String fileRevision = nameParts[4];
                
                // 处理.verf文件（没有文件序号部分）
                if (name.endsWith(".verf")) {
                    fileRevision = fileRevision.replace(".verf", "");
                } else if (name.endsWith(".dat")) {
                    // 对于.dat文件，需要去除文件序号部分
                    fileRevision = fileRevision.split("\\.")[0];
                }
                
                // 同时匹配接口ID和版本号
                return interfaceId.equals(fileInterfaceId) && revision.equals(fileRevision);
            }
            return false;
        });
        
        if (files != null) {
            for (File file : files) {
                try {
                    String fileType = file.getName().endsWith(".dat") ? "dat" : "verf";
                    
                    FileTransferRequest.FileInfo fileInfo = FileTransferRequest.FileInfo.builder()
                            .fileName(file.getName())
                            .filePath(file.getAbsolutePath())
                            .fileType(fileType)
                            .fileSize(file.length())
                            .createTime(LocalDateTime.now())
                            .build();
                    
                    fileList.add(fileInfo);
                    log.debug("添加文件到传输清单: {}, 大小: {} bytes", file.getName(), file.length());
                    
                } catch (Exception e) {
                    log.warn("处理文件信息失败: {}", file.getAbsolutePath(), e);
                }
            }
        }
        
        log.info("扫描输出目录完成 - 目录: {}, 找到文件: {} 个", outputDir, fileList.size());
        return fileList;
    }
    
    /**
     * 发送通知到DMZ
     * 
     * @param request 文件传输请求
     * @return DMZ响应
     */
    private FileTransferResponse sendNotificationToDmz(FileTransferRequest request) throws VgopException {
        VgopAppConfig.DmzConfig dmzConfig = appConfig.getDmz();
        
        // 构建DMZ服务URL
        String dmzUrl = buildDmzUrl(dmzConfig);
        
        log.info("发送文件清单通知到DMZ - URL: {}, 文件数: {}", dmzUrl, request.getFileList().size());
        
        try {
            // 创建RestTemplate
            RestTemplate restTemplate = new RestTemplate();
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            // 添加基础认证（如果配置了用户名密码）
            if (dmzConfig.getUsername() != null && dmzConfig.getPassword() != null) {
                String auth = dmzConfig.getUsername() + ":" + dmzConfig.getPassword();
                String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
                headers.set("Authorization", "Basic " + encodedAuth);
            }
            
            // 创建请求实体
            HttpEntity<FileTransferRequest> requestEntity = new HttpEntity<>(request, headers);
            
            // 使用正确的泛型类型来反序列化ApiResponse<FileTransferResponse>
            ParameterizedTypeReference<ApiResponse<FileTransferResponse>> responseType = 
                    new ParameterizedTypeReference<ApiResponse<FileTransferResponse>>() {};
            
            // 发送请求并获取响应
            ResponseEntity<ApiResponse<FileTransferResponse>> responseEntity = restTemplate.exchange(
                    dmzUrl, HttpMethod.POST, requestEntity, responseType);
            
            // 添加详细的响应调试信息
            log.debug("DMZ HTTP响应详情 - 状态码: {}, 响应头: {}", 
                     responseEntity.getStatusCode(), responseEntity.getHeaders());
            
            if (responseEntity.getStatusCode() == HttpStatus.OK && responseEntity.getBody() != null) {
                ApiResponse<FileTransferResponse> apiResponse = responseEntity.getBody();
                
                // 记录API响应信息
                log.info("DMZ API响应 - 状态码: {}, 消息: {}, 数据状态: {}", 
                         apiResponse.getCode(), apiResponse.getMessage(), 
                         apiResponse.getData() != null ? apiResponse.getData().getStatus() : "null");
                
                // 添加数据字段的详细调试信息
                if (apiResponse.getData() != null) {
                    FileTransferResponse data = apiResponse.getData();
                    log.debug("DMZ响应数据详情 - 任务ID: {}, 状态: {}, 总文件: {}, 成功: {}, 失败: {}, 消息: {}", 
                             data.getTaskId(), data.getStatus(), data.getTotalFiles(), 
                             data.getSuccessFiles(), data.getFailedFiles(), data.getMessage());
                } else {
                    log.warn("DMZ API响应中data字段为null - 可能存在JSON反序列化问题");
                }
                
                // 检查API响应状态
                if (apiResponse.getCode() == 200 && apiResponse.getData() != null) {
                    return apiResponse.getData();
                } else {
                    // 如果API响应不成功，但message表示成功，创建一个基于message的成功响应
                    if (apiResponse.getMessage() != null && 
                        (apiResponse.getMessage().contains("处理完成") || apiResponse.getMessage().contains("成功"))) {
                        log.warn("DMZ API响应状态码非200，但message表示成功，创建降级响应 - 消息: {}", apiResponse.getMessage());
                        return createSuccessResponseFromMessage(request.getTaskId(), apiResponse.getMessage());
                    } else {
                        throw new VgopException("DMZ API响应失败 - 状态码: " + apiResponse.getCode() + ", 消息: " + apiResponse.getMessage());
                    }
                }
            } else {
                log.error("DMZ HTTP响应异常 - 状态码: {}, 响应体是否为null: {}", 
                         responseEntity.getStatusCode(), responseEntity.getBody() == null);
                throw new VgopException("DMZ HTTP响应异常 - HTTP状态: " + responseEntity.getStatusCode());
            }
            
        } catch (Exception e) {
            log.error("发送DMZ通知请求失败: {}", dmzUrl, e);
            
            // 智能降级机制：分析异常类型和消息内容
            String errorMessage = e.getMessage();
            
            // 1. 检查是否为JSON解析异常但包含成功信息
            if (e instanceof org.springframework.web.client.RestClientException) {
                if (errorMessage != null && 
                    (errorMessage.contains("处理完成") || errorMessage.contains("文件中转处理完成") || 
                     errorMessage.contains("SUCCESS") || errorMessage.contains("成功"))) {
                    log.warn("REST客户端异常，但错误信息表示处理成功，创建降级响应 - 错误: {}", errorMessage);
                    return createSuccessResponseFromMessage(request.getTaskId(), 
                            "DMZ处理可能成功，但响应解析失败: " + errorMessage);
                }
            }
            
            // 2. 检查是否为HTTP连接异常
            if (e instanceof java.net.ConnectException || e instanceof java.net.SocketTimeoutException) {
                log.error("DMZ连接异常 - 可能是网络问题或DMZ服务不可用: {}", errorMessage);
                throw new VgopException("DMZ连接失败，请检查网络连接和DMZ服务状态: " + errorMessage, e);
            }
            
            // 3. 检查是否为认证异常
            if (e instanceof org.springframework.web.client.HttpClientErrorException) {
                org.springframework.web.client.HttpClientErrorException httpException = 
                        (org.springframework.web.client.HttpClientErrorException) e;
                if (httpException.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                    log.error("DMZ认证失败 - 请检查用户名密码配置: {}", errorMessage);
                    throw new VgopException("DMZ认证失败，请检查配置的用户名密码", e);
                }
            }
            
            // 4. 默认异常处理
            log.error("DMZ通知请求发生未知异常 - URL: {}, 异常类型: {}, 消息: {}", 
                     dmzUrl, e.getClass().getSimpleName(), errorMessage);
            throw new VgopException("发送DMZ通知请求失败: " + errorMessage, e);
        }
    }
    
    /**
     * 构建DMZ服务URL
     * 
     * @param dmzConfig DMZ配置
     * @return 完整的DMZ服务URL
     */
    private String buildDmzUrl(VgopAppConfig.DmzConfig dmzConfig) {
        String baseUrl = dmzConfig.getBaseUrl();
        if (!baseUrl.startsWith("http://") && !baseUrl.startsWith("https://")) {
            baseUrl = "http://" + baseUrl;
        }
        
        if (dmzConfig.getPort() != 80 && dmzConfig.getPort() != 443) {
            baseUrl += ":" + dmzConfig.getPort();
        }
        
        String path = dmzConfig.getFileListNotifyPath();
        if (!path.startsWith("/")) {
            path = "/" + path;
        }
        
        return baseUrl + path;
    }
    
    /**
     * 创建空响应
     * 
     * @param taskId 任务ID
     * @param message 消息
     * @return 响应对象
     */
    private FileTransferResponse createEmptyResponse(String taskId, String message) {
        return FileTransferResponse.builder()
                .taskId(taskId)
                .status("SUCCESS")
                .responseTime(LocalDateTime.now())
                .totalFiles(0)
                .successFiles(0)
                .failedFiles(0)
                .notFoundFiles(0)
                .successFileList(new ArrayList<>())
                .failedFileList(new ArrayList<>())
                .message(message)
                .build();
    }
    
    /**
     * 创建错误响应
     * 
     * @param taskId 任务ID
     * @param errorMessage 错误消息
     * @return 响应对象
     */
    private FileTransferResponse createErrorResponse(String taskId, String errorMessage) {
        return FileTransferResponse.builder()
                .taskId(taskId)
                .status("FAILED")
                .responseTime(LocalDateTime.now())
                .totalFiles(0)
                .successFiles(0)
                .failedFiles(0)
                .notFoundFiles(0)
                .successFileList(new ArrayList<>())
                .failedFileList(new ArrayList<>())
                .message(errorMessage)
                .errorDetail(errorMessage)
                .build();
    }

    /**
     * 根据消息创建成功响应（降级机制）
     * 
     * @param taskId 任务ID
     * @param message 消息内容
     * @return 成功响应对象
     */
    private FileTransferResponse createSuccessResponseFromMessage(String taskId, String message) {
        return FileTransferResponse.builder()
                .taskId(taskId)
                .status("SUCCESS")
                .responseTime(LocalDateTime.now())
                .totalFiles(0)
                .successFiles(0)
                .failedFiles(0)
                .notFoundFiles(0)
                .successFileList(new ArrayList<>())
                .failedFileList(new ArrayList<>())
                .message(message)
                .build();
    }
} 