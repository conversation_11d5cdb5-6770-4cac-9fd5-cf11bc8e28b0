<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vgop.service.dao.TaskExecutionMapper">

    <resultMap id="TaskExecutionResultMap" type="com.vgop.service.entity.TaskExecution">
        <id column="id" property="id"/>
        <result column="task_type" property="taskType"/>
        <result column="data_date" property="dataDate"/>
        <result column="stage" property="stage"/>
        <result column="revision" property="revision"/>
        <result column="status" property="status"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="duration" property="duration"/>
        <result column="error_message" property="errorMessage"/>
        <result column="processed_count" property="processedCount"/>
        <result column="success_count" property="successCount"/>
        <result column="fail_count" property="failCount"/>
        <result column="extra_info" property="extraInfo"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <select id="findById" resultMap="TaskExecutionResultMap">
        SELECT id, task_type, data_date, stage, revision, status, start_time, end_time, 
               duration, error_message, processed_count, success_count, fail_count, 
               extra_info, create_time, update_time
        FROM vgop_task_execution
        WHERE id = #{id}
    </select>

    <select id="findByDataDateAndType" resultMap="TaskExecutionResultMap">
        SELECT id, task_type, data_date, stage, revision, status, start_time, end_time, 
               duration, error_message, processed_count, success_count, fail_count, 
               extra_info, create_time, update_time
        FROM vgop_task_execution
        WHERE data_date = #{dataDate} AND task_type = #{taskType}
        ORDER BY create_time DESC
    </select>

    <select id="findByStatus" resultMap="TaskExecutionResultMap">
        SELECT id, task_type, data_date, stage, revision, status, start_time, end_time, 
               duration, error_message, processed_count, success_count, fail_count, 
               extra_info, create_time, update_time
        FROM vgop_task_execution
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

    <insert id="insert" parameterType="com.vgop.service.entity.TaskExecution" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO vgop_task_execution (task_type, data_date, stage, revision, status, start_time, 
                                        end_time, duration, error_message, processed_count, 
                                        success_count, fail_count, extra_info, create_time, update_time)
        VALUES (#{taskType}, #{dataDate}, #{stage}, #{revision}, #{status}, 
                #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, 
                #{duration}, #{errorMessage}, #{processedCount}, 
                #{successCount}, #{failCount}, #{extraInfo}, 
                #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>

    <update id="updateStatus">
        UPDATE vgop_task_execution
        SET status = #{status}, 
            end_time = #{endTime,jdbcType=TIMESTAMP}, 
            duration = #{duration}, 
            error_message = #{errorMessage},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id}
    </update>

    <update id="updateProgress">
        UPDATE vgop_task_execution
        SET processed_count = #{processedCount}, 
            success_count = #{successCount}, 
            fail_count = #{failCount},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        WHERE id = #{id}
    </update>

</mapper> 