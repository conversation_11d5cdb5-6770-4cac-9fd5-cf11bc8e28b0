{"groups": [{"name": "app", "type": "com.vgop.service.config.VgopAppConfig", "sourceType": "com.vgop.service.config.VgopAppConfig"}, {"name": "app.alert", "type": "com.vgop.service.config.VgopAppConfig$AlertConfig", "sourceType": "com.vgop.service.config.VgopAppConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.AlertConfig getAlert() "}, {"name": "app.alert.storage", "type": "com.vgop.service.config.VgopAppConfig$AlertConfig$Storage", "sourceType": "com.vgop.service.config.VgopAppConfig$AlertConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.AlertConfig.Storage getStorage() "}, {"name": "app.alert.thresholds", "type": "com.vgop.service.config.VgopAppConfig$AlertConfig$Thresholds", "sourceType": "com.vgop.service.config.VgopAppConfig$AlertConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.AlertConfig.Thresholds getThresholds() "}, {"name": "app.alert.upload-deadline", "type": "com.vgop.service.config.VgopAppConfig$AlertConfig$UploadDeadline", "sourceType": "com.vgop.service.config.VgopAppConfig$AlertConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.AlertConfig.UploadDeadline getUploadDeadline() "}, {"name": "app.backup", "type": "com.vgop.service.config.VgopAppConfig$BackupConfig", "sourceType": "com.vgop.service.config.VgopAppConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.BackupConfig getBackup() "}, {"name": "app.base-path", "type": "com.vgop.service.config.VgopAppConfig$BasePath", "sourceType": "com.vgop.service.config.VgopAppConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.BasePath getBasePath() "}, {"name": "app.dmz", "type": "com.vgop.service.config.VgopAppConfig$DmzConfig", "sourceType": "com.vgop.service.config.VgopAppConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.DmzConfig getDmz() "}, {"name": "app.file-processing", "type": "com.vgop.service.config.VgopAppConfig$FileProcessing", "sourceType": "com.vgop.service.config.VgopAppConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.FileProcessing getFileProcessing() "}, {"name": "app.schedules", "type": "com.vgop.service.config.VgopAppConfig$Schedules", "sourceType": "com.vgop.service.config.VgopAppConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.Schedules getSchedules() "}, {"name": "app.sftp", "type": "com.vgop.service.config.VgopAppConfig$SftpConfig", "sourceType": "com.vgop.service.config.VgopAppConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.SftpConfig getSftp() "}, {"name": "app.tasks", "type": "com.vgop.service.config.VgopAppConfig$Tasks", "sourceType": "com.vgop.service.config.VgopAppConfig", "sourceMethod": "public com.vgop.service.config.VgopAppConfig.Tasks getTasks() "}, {"name": "spring.datasource.primary", "type": "javax.sql.DataSource", "sourceType": "com.vgop.service.config.DataSourceConfig", "sourceMethod": "public javax.sql.DataSource primaryDataSource() "}, {"name": "spring.datasource.secondary", "type": "javax.sql.DataSource", "sourceType": "com.vgop.service.config.DataSourceConfig", "sourceMethod": "public javax.sql.DataSource secondaryDataSource() "}, {"name": "validation", "type": "com.vgop.service.config.ValidationRulesProperties", "sourceType": "com.vgop.service.config.ValidationRulesProperties"}, {"name": "vgop", "type": "com.vgop.service.config.VgopProperties", "sourceType": "com.vgop.service.config.VgopProperties"}, {"name": "vgop.data-quality", "type": "com.vgop.service.config.VgopProperties$DataQuality", "sourceType": "com.vgop.service.config.VgopProperties", "sourceMethod": "public com.vgop.service.config.VgopProperties.DataQuality getDataQuality() "}, {"name": "vgop.database", "type": "com.vgop.service.config.VgopProperties$Database", "sourceType": "com.vgop.service.config.VgopProperties", "sourceMethod": "public com.vgop.service.config.VgopProperties.Database getDatabase() "}, {"name": "vgop.file-processing", "type": "com.vgop.service.config.VgopProperties$FileProcessing", "sourceType": "com.vgop.service.config.VgopProperties", "sourceMethod": "public com.vgop.service.config.VgopProperties.FileProcessing getFileProcessing() "}, {"name": "vgop.scheduler", "type": "com.vgop.service.config.VgopProperties$Scheduler", "sourceType": "com.vgop.service.config.VgopProperties", "sourceMethod": "public com.vgop.service.config.VgopProperties.Scheduler getScheduler() "}], "properties": [{"name": "app.alert.storage.alert-file-path", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$AlertConfig$Storage"}, {"name": "app.alert.storage.enable-database", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.vgop.service.config.VgopAppConfig$AlertConfig$Storage"}, {"name": "app.alert.storage.enable-file", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.vgop.service.config.VgopAppConfig$AlertConfig$Storage"}, {"name": "app.alert.thresholds.max-alerts-per-batch", "type": "java.lang.Integer", "sourceType": "com.vgop.service.config.VgopAppConfig$AlertConfig$Thresholds"}, {"name": "app.alert.upload-deadline.daily", "type": "java.lang.Integer", "sourceType": "com.vgop.service.config.VgopAppConfig$AlertConfig$UploadDeadline"}, {"name": "app.alert.upload-deadline.monthly", "type": "java.lang.Integer", "sourceType": "com.vgop.service.config.VgopAppConfig$AlertConfig$UploadDeadline"}, {"name": "app.backup.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.vgop.service.config.VgopAppConfig$BackupConfig"}, {"name": "app.backup.retention-days", "type": "java.lang.Integer", "sourceType": "com.vgop.service.config.VgopAppConfig$BackupConfig"}, {"name": "app.base-path.backup-root", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$BasePath"}, {"name": "app.base-path.export-root", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$BasePath"}, {"name": "app.base-path.log-root", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$BasePath"}, {"name": "app.dmz.base-url", "type": "java.lang.String", "description": "DMZ服务地址", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.dmz.connection-timeout", "type": "java.lang.Integer", "description": "连接超时时间（毫秒）", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.dmz.downstream-sftp-list", "type": "java.util.List<com.vgop.service.config.VgopAppConfig$DmzConfig$DownstreamSftpConfig>", "description": "下游SFTP配置（支持多个服务器）", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.dmz.file-list-notify-path", "type": "java.lang.String", "description": "文件清单通知接口路径", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.dmz.local-file-directory", "type": "java.lang.String", "description": "DMZ本地文件存储目录", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.dmz.password", "type": "java.lang.String", "description": "DMZ服务认证密码", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.dmz.port", "type": "java.lang.Integer", "description": "DMZ服务端口", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.dmz.read-timeout", "type": "java.lang.Integer", "description": "读取超时时间（毫秒）", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.dmz.retry-times", "type": "java.lang.Integer", "description": "重试次数", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.dmz.username", "type": "java.lang.String", "description": "DMZ服务认证用户名", "sourceType": "com.vgop.service.config.VgopAppConfig$DmzConfig"}, {"name": "app.enabled-daily-tasks", "type": "java.util.List<com.vgop.service.config.TaskConfig>", "sourceType": "com.vgop.service.config.VgopAppConfig"}, {"name": "app.enabled-monthly-tasks", "type": "java.util.List<com.vgop.service.config.TaskConfig>", "sourceType": "com.vgop.service.config.VgopAppConfig"}, {"name": "app.file-processing.add-line-number", "type": "java.lang.Bo<PERSON>an", "sourceType": "com.vgop.service.config.VgopAppConfig$FileProcessing"}, {"name": "app.file-processing.default-delimiter", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$FileProcessing"}, {"name": "app.file-processing.line-ending", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$FileProcessing"}, {"name": "app.file-processing.line-number-delimiter", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$FileProcessing"}, {"name": "app.file-processing.max-rows-per-file", "type": "java.lang.Integer", "sourceType": "com.vgop.service.config.VgopAppConfig$FileProcessing"}, {"name": "app.file-processing.output-delimiter", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$FileProcessing"}, {"name": "app.schedules.daily-cron", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$Schedules"}, {"name": "app.schedules.monthly-cron", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$Schedules"}, {"name": "app.sftp.connection-timeout", "type": "java.lang.Integer", "sourceType": "com.vgop.service.config.VgopAppConfig$SftpConfig"}, {"name": "app.sftp.host", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$SftpConfig"}, {"name": "app.sftp.max-pool-size", "type": "java.lang.Integer", "sourceType": "com.vgop.service.config.VgopAppConfig$SftpConfig"}, {"name": "app.sftp.password", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$SftpConfig"}, {"name": "app.sftp.port", "type": "java.lang.Integer", "sourceType": "com.vgop.service.config.VgopAppConfig$SftpConfig"}, {"name": "app.sftp.remote-base-path", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$SftpConfig"}, {"name": "app.sftp.retry-times", "type": "java.lang.Integer", "sourceType": "com.vgop.service.config.VgopAppConfig$SftpConfig"}, {"name": "app.sftp.username", "type": "java.lang.String", "sourceType": "com.vgop.service.config.VgopAppConfig$SftpConfig"}, {"name": "app.tasks.daily", "type": "java.util.List<com.vgop.service.config.TaskConfig>", "sourceType": "com.vgop.service.config.VgopAppConfig$Tasks"}, {"name": "app.tasks.monthly", "type": "java.util.List<com.vgop.service.config.TaskConfig>", "sourceType": "com.vgop.service.config.VgopAppConfig$Tasks"}, {"name": "validation.interfaces", "type": "java.util.Map<java.lang.String,com.vgop.service.config.ValidationRulesProperties$InterfaceConfig>", "description": "接口配置映射 key: 接口名 (e.g., \"VGOP1-R2.10-24201\") value: 该接口对应的校验规则配置列表", "sourceType": "com.vgop.service.config.ValidationRulesProperties"}, {"name": "vgop.data-quality.enable-quality-check", "type": "java.lang.Bo<PERSON>an", "description": "是否启用数据质量检测", "sourceType": "com.vgop.service.config.VgopProperties$DataQuality"}, {"name": "vgop.data-quality.generate-non-compliance-report", "type": "java.lang.Bo<PERSON>an", "description": "是否生成不合规数据Excel报告", "sourceType": "com.vgop.service.config.VgopProperties$DataQuality"}, {"name": "vgop.data-quality.max-report-rows", "type": "java.lang.Integer", "description": "Excel报告最大行数限制", "sourceType": "com.vgop.service.config.VgopProperties$DataQuality"}, {"name": "vgop.data-quality.non-compliance-report-template", "type": "java.lang.String", "description": "不合规数据报告文件名模板", "sourceType": "com.vgop.service.config.VgopProperties$DataQuality"}, {"name": "vgop.data-quality.non-compliance-threshold", "type": "java.lang.Double", "description": "不合规数据占比阈值（默认0.1，即10%）", "sourceType": "com.vgop.service.config.VgopProperties$DataQuality"}, {"name": "vgop.data-quality.volume-fluctuation-threshold", "type": "java.lang.Double", "description": "数据量波动阈值（默认0.1，即10%）", "sourceType": "com.vgop.service.config.VgopProperties$DataQuality"}, {"name": "vgop.database.connection-timeout", "type": "java.lang.Integer", "description": "数据库连接超时时间（秒）", "sourceType": "com.vgop.service.config.VgopProperties$Database"}, {"name": "vgop.database.default-column-separator", "type": "java.lang.String", "description": "默认列分隔符", "sourceType": "com.vgop.service.config.VgopProperties$Database"}, {"name": "vgop.database.lock-wait-time", "type": "java.lang.Integer", "description": "锁等待时间（秒）", "sourceType": "com.vgop.service.config.VgopProperties$Database"}, {"name": "vgop.database.query-timeout", "type": "java.lang.Integer", "description": "查询超时时间（秒）", "sourceType": "com.vgop.service.config.VgopProperties$Database"}, {"name": "vgop.file-processing.data-file-suffix", "type": "java.lang.String", "description": "数据文件后缀", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.file-processing.data-root-path", "type": "java.lang.String", "description": "数据文件根目录", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.file-processing.day-sub-path", "type": "java.lang.String", "description": "日统计子目录", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.file-processing.file-encoding", "type": "java.lang.String", "description": "文件编码", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.file-processing.line-delimiter-replacement", "type": "java.lang.String", "description": "行分隔符替换字符 (ASCII 200)", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.file-processing.line-ending", "type": "java.lang.String", "description": "行结束符", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.file-processing.max-lines-per-file", "type": "java.lang.Integer", "description": "每个文件的最大行数", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.file-processing.month-sub-path", "type": "java.lang.String", "description": "月统计子目录", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.file-processing.temp-file-suffix", "type": "java.lang.String", "description": "临时文件后缀", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.file-processing.verf-file-suffix", "type": "java.lang.String", "description": "校验文件后缀", "sourceType": "com.vgop.service.config.VgopProperties$FileProcessing"}, {"name": "vgop.scheduler.core-pool-size", "type": "java.lang.Integer", "description": "线程池核心线程数", "sourceType": "com.vgop.service.config.VgopProperties$Scheduler"}, {"name": "vgop.scheduler.keep-alive-seconds", "type": "java.lang.Integer", "description": "线程空闲时间（秒）", "sourceType": "com.vgop.service.config.VgopProperties$Scheduler"}, {"name": "vgop.scheduler.max-pool-size", "type": "java.lang.Integer", "description": "线程池最大线程数", "sourceType": "com.vgop.service.config.VgopProperties$Scheduler"}, {"name": "vgop.scheduler.queue-capacity", "type": "java.lang.Integer", "description": "队列容量", "sourceType": "com.vgop.service.config.VgopProperties$Scheduler"}, {"name": "vgop.scheduler.task-timeout-minutes", "type": "java.lang.Integer", "description": "任务执行超时时间（分钟）", "sourceType": "com.vgop.service.config.VgopProperties$Scheduler"}], "hints": []}