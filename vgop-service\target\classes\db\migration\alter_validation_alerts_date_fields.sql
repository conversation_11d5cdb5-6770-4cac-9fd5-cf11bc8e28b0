-- 数据库表结构迁移脚本：将日期字段改为字符串类型
-- 文件：alter_validation_alerts_date_fields.sql
-- 说明：将vgop_validation_alerts表的alert_time和handled_time字段从DATETIME类型改为VARCHAR(14)类型

-- ====================
-- 1. 备份现有数据（可选）
-- ====================
-- CREATE TABLE vgop_validation_alerts_backup AS SELECT * FROM vgop_validation_alerts;

-- ====================
-- 2. 添加新的字符串类型字段
-- ====================
ALTER TABLE vgop_validation_alerts ADD COLUMN alert_time_new VARCHAR(14);
ALTER TABLE vgop_validation_alerts ADD COLUMN handled_time_new VARCHAR(14);

-- ====================
-- 3. 数据迁移：将现有DATETIME数据转换为字符串格式
-- ====================

-- 针对Informix数据库的迁移语句
-- 转换alert_time字段（非空字段）
UPDATE vgop_validation_alerts 
SET alert_time_new = CASE 
    WHEN alert_time IS NOT NULL THEN 
        EXTEND(alert_time, YEAR TO SECOND)::CHAR(19)
        -- 移除分隔符，转换为YYYYMMDDHHMMSS格式
        REPLACE(
            REPLACE(
                REPLACE(
                    REPLACE(EXTEND(alert_time, YEAR TO SECOND)::CHAR(19), '-', ''),
                    ' ', ''
                ),
                ':', ''
            ),
            '.', ''
        )
    ELSE ''
END;

-- 转换handled_time字段（可空字段）
UPDATE vgop_validation_alerts 
SET handled_time_new = CASE 
    WHEN handled_time IS NOT NULL THEN 
        REPLACE(
            REPLACE(
                REPLACE(
                    REPLACE(EXTEND(handled_time, YEAR TO SECOND)::CHAR(19), '-', ''),
                    ' ', ''
                ),
                ':', ''
            ),
            '.', ''
        )
    ELSE NULL
END;

-- ====================
-- 4. 验证数据迁移结果
-- ====================
-- SELECT alert_id, alert_time, alert_time_new, handled_time, handled_time_new 
-- FROM vgop_validation_alerts 
-- WHERE alert_time_new IS NULL OR alert_time_new = '' 
-- LIMIT 10;

-- ====================
-- 5. 删除原字段
-- ====================
ALTER TABLE vgop_validation_alerts DROP COLUMN alert_time;
ALTER TABLE vgop_validation_alerts DROP COLUMN handled_time;

-- ====================
-- 6. 重命名新字段
-- ====================
-- 注意：Informix的字段重命名语法
-- ALTER TABLE vgop_validation_alerts RENAME COLUMN alert_time_new TO alert_time;
-- ALTER TABLE vgop_validation_alerts RENAME COLUMN handled_time_new TO handled_time;

-- 如果不支持RENAME COLUMN，则使用以下方式：
-- 先添加最终字段
ALTER TABLE vgop_validation_alerts ADD COLUMN alert_time VARCHAR(14) DEFAULT '' NOT NULL;
ALTER TABLE vgop_validation_alerts ADD COLUMN handled_time VARCHAR(14);

-- 复制数据
UPDATE vgop_validation_alerts SET alert_time = COALESCE(alert_time_new, '');
UPDATE vgop_validation_alerts SET handled_time = handled_time_new;

-- 删除临时字段
ALTER TABLE vgop_validation_alerts DROP COLUMN alert_time_new;
ALTER TABLE vgop_validation_alerts DROP COLUMN handled_time_new;

-- ====================
-- 7. 重建索引
-- ====================
-- 删除旧索引（如果存在）
DROP INDEX IF EXISTS idx_alerts_time;

-- 创建新索引
CREATE INDEX idx_alerts_time ON vgop_validation_alerts(alert_time);

-- ====================
-- 8. 验证最终结果
-- ====================
-- 检查表结构
-- DESC vgop_validation_alerts;

-- 检查数据示例
-- SELECT alert_id, alert_time, handled_time, interface_name, alert_message 
-- FROM vgop_validation_alerts 
-- ORDER BY alert_time DESC 
-- LIMIT 5;

-- ====================
-- 9. 针对H2数据库的等效脚本（用于测试环境）
-- ====================

/*
-- H2数据库版本的迁移脚本
ALTER TABLE vgop_validation_alerts ADD COLUMN alert_time_new VARCHAR(14);
ALTER TABLE vgop_validation_alerts ADD COLUMN handled_time_new VARCHAR(14);

UPDATE vgop_validation_alerts 
SET alert_time_new = CASE 
    WHEN alert_time IS NOT NULL THEN 
        FORMATDATETIME(alert_time, 'yyyyMMddHHmmss')
    ELSE ''
END;

UPDATE vgop_validation_alerts 
SET handled_time_new = CASE 
    WHEN handled_time IS NOT NULL THEN 
        FORMATDATETIME(handled_time, 'yyyyMMddHHmmss')
    ELSE NULL
END;

ALTER TABLE vgop_validation_alerts DROP COLUMN alert_time;
ALTER TABLE vgop_validation_alerts DROP COLUMN handled_time;

ALTER TABLE vgop_validation_alerts ALTER COLUMN alert_time_new RENAME TO alert_time;
ALTER TABLE vgop_validation_alerts ALTER COLUMN handled_time_new RENAME TO handled_time;

-- 重建索引
DROP INDEX IF EXISTS idx_alerts_time;
CREATE INDEX idx_alerts_time ON vgop_validation_alerts(alert_time);
*/ 