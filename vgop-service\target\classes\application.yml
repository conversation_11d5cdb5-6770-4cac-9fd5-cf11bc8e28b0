# VGOP数据校验系统配置文件
spring:
  application:
    name: vgop-service
  
  # 默认激活开发环境配置
  profiles:
    active: dev
  
  # <PERSON>配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # Web配置
  web:
    resources:
      add-mappings: false
  mvc:
    throw-exception-if-no-handler-found: true

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /vgop
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  undertow:
    threads:
      io: 8
      worker: 64
    buffer-size: 1024
    direct-buffers: true

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.vgop.service.entity
  configuration:
    map-underscore-to-camel-case: true
    default-fetch-size: 100
    default-statement-timeout: 30
    cache-enabled: true
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 日志配置
logging:
  level:
    root: INFO
    com.vgop.service: DEBUG
    org.springframework: INFO
    org.mybatis: DEBUG
    org.springframework.jdbc: DEBUG
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n'
  file:
    name: logs/vgop-service.log
    max-size: 100MB
    max-history: 30

# Actuator监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized

# VGOP应用核心配置
app:
  # 定时任务触发时间
  schedules:
    daily-cron: "0 */5 * * * ?"    # 日数据：每日凌晨0点
    monthly-cron: "0 */5 * * * ?"  # 月数据：每月1日凌晨5点
  # schedules:
  # daily-cron: "0 0 0 * * ?"    # 日数据：每日凌晨0点
  # monthly-cron: "0 0 5 1 * ?"  # 月数据：每月1日凌晨5点
  # 根目录配置
  base-path:
    # 数据导出根目录 (数据库服务器可访问的路径)
    export-root: "./data/VGOPdata/datafile/"
    # 日志目录
    log-root: "./data/vgop/log/"
    # 备份目录
    backup-root: "./data/vgop/backup/"
  
  # SFTP配置
  sftp:
    host: "***********"
    port: 19222
    username: "javadev1"
    password: "1qaz@WSX"
    remote-base-path: "/upload/"
    connection-timeout: 5000
    retry-times: 1
    
  # 文件处理配置
  file-processing:
    max-rows-per-file: 2000000  # 每个文件最大行数
    default-delimiter: "|"       # 默认字段分隔符
    output-delimiter: "|"     # 输出文件分隔符（八进制）
    line-ending: "\r"            # 行结束符（回车）
    add-line-number: true        # 是否添加行号
    line-number-delimiter: "|"   # 行号与数据之间的分隔符
    
  # 告警配置
  alert:
    # 告警存储配置
    storage:
      enable-database: true    # 启用数据库存储
      enable-file: false       # 是否同时保存到文件
      alert-file-path: "/data/vgop/alerts/"  # 告警文件保存路径
      
    # 上传时间监控（小时）
    upload-deadline:
      daily: 8    # 每日8点前
      monthly: 8  # 每月1日8点前
      
    # 告警阈值配置
    thresholds:
      max-alerts-per-batch: 1000  # 批量保存的最大告警数
      
  # 兜底措施配置
  backup:
    retention-days: 7  # 保留7天的备份
    enabled: true      # 是否启用备份

# Swagger配置
springfox:
  documentation:
    enabled: true
    swagger-ui:
      enabled: true

# VGOP自定义配置
vgop:
  database:
    default-column-separator: "|"
    connection-timeout: 30
    query-timeout: 300
    lock-wait-time: 10
    
  file-processing:
    max-lines-per-file: 20000000
    data-root-path: "./VGOPdata/datafile"
    day-sub-path: "day"
    month-sub-path: "month"
    temp-file-suffix: ".unl"
    data-file-suffix: ".dat"
    verf-file-suffix: ".verf"
    file-encoding: "UTF-8"
    line-delimiter-replacement: "\\200"
    line-ending: "\\r"
    
  scheduler:
    core-pool-size: 5
    max-pool-size: 10
    queue-capacity: 100
    keep-alive-seconds: 60
    task-timeout-minutes: 60 